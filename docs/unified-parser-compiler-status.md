# Unified Parser-Compiler Status

## Current Status

The unified parser-compiler has been implemented and is available via the `fastrun` (`fr`) command. It successfully:

1. **Parses and compiles in a single pass** - Eliminates the intermediate AST
2. **Supports basic Gene syntax**:
   - Literals (numbers, strings, symbols, keywords)
   - Collections (arrays, maps)
   - S-expressions (genes)
   - Comments

3. **Provides fail-fast error handling** - Stops immediately on syntax errors with line/column info

4. **Is ready for caching** - Phase 2 caching plan documented in `parser-compiler-caching.md`

## Current Issues

The unified parser-compiler has runtime compatibility issues with the VM:

1. **Function call vs Gene literal ambiguity**: 
   - Current implementation uses `IkGeneStartDefault` for all S-expressions
   - VM expects different bytecode patterns for function calls vs gene literals
   - Need to implement proper compile-time detection of special forms

2. **Label handling**:
   - Labels for macro/function branching need proper implementation
   - Current placeholder approach causes runtime errors

3. **Special forms**:
   - Need to handle `if`, `let`, `do`, `fn`, etc. specially
   - Currently treating everything as runtime-resolved

## To Make It Default

To replace the existing parser/compiler with the unified version:

### 1. Fix Function Call Generation
```nim
# Instead of always using IkGeneStartDefault, detect:
- Known functions -> generate direct calls
- Special forms -> compile specially  
- Macros -> generate macro expansion
- Unknown -> use IkGeneStartDefault for runtime resolution
```

### 2. Implement Special Forms
Port the special form handling from `compiler.nim`:
- `if`, `let`, `do`, `fn`, `def`, `var`
- `loop`, `while`, `for`, `repeat`
- `try`, `throw`, `return`
- `class`, `new`, `super`

### 3. Fix Label Management
```nim
# Proper label generation for:
- Jump targets in control flow
- Macro/function branch points
- Loop start/end points
```

### 4. Add Missing Features
- Quote/unquote handling
- Namespace and import support
- Method calls and selectors
- Operator precedence

## Integration Path

1. **Keep both implementations** (current state)
   - Original: `gene run` (default)
   - Unified: `gene fastrun` (experimental)

2. **Gradual migration**:
   - Fix runtime issues one by one
   - Add comprehensive tests comparing outputs
   - Enable unified compiler for specific use cases

3. **Switch default**:
   - Once all tests pass with unified compiler
   - Keep original as fallback: `gene run --legacy`
   - Eventually remove original implementation

## Performance Comparison

Expected performance improvements (once working):
- **Parse + Compile**: 30-50% faster (single pass vs two passes)
- **Memory usage**: 40-60% less (no AST allocation)
- **With caching**: 10-100x faster for unchanged files

## Testing Strategy

To ensure compatibility:

1. **Bytecode comparison tests**:
```nim
test "generates same bytecode":
  let code = "(+ 1 2)"
  let old_bytecode = compile(parse(code))
  let new_bytecode = parse_compile_string(code)
  check old_bytecode == new_bytecode
```

2. **Execution tests**:
```nim
test "produces same results":
  let code = "(+ 1 2)"
  let old_result = VM.exec(compile(parse(code)))
  let new_result = VM.exec(parse_compile_string(code))
  check old_result == new_result
```

3. **Performance benchmarks**:
```nim
benchmark "compilation speed":
  let old_time = measure: compile(parse(large_code))
  let new_time = measure: parse_compile_string(large_code)
  check new_time < old_time * 0.7  # At least 30% faster
```

## Conclusion

The unified parser-compiler is architecturally sound and provides the foundation for significant performance improvements. However, it needs additional work to achieve full compatibility with the existing VM bytecode expectations before it can become the default implementation.