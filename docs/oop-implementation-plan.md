# OOP Implementation Plan for Gene VM

## Current State
The VM has basic OOP support with:
- `IkClass`, `IkSubClass`, `IkNew` instructions
- Basic class creation and instantiation
- Simple constructor support
- `IkResolveMethod` for method binding

## Core OOP Features from gene-new

### 1. Classes
- [x] Class definition: `(class Name body...)`
- [x] Inheritance: `(class Child < Parent body...)`
- [x] Constructor: `.ctor` method
- [ ] Class namespaces (each class has its own namespace)
- [ ] Class properties and methods stored in namespace

### 2. Methods
- [ ] Instance methods: `(.fn name args body...)`
- [ ] Method binding to classes/mixins
- [ ] Method resolution through inheritance chain
- [ ] `super` for calling parent methods with arguments
- [ ] Method dispatch with self binding

### 3. Mixins
- [ ] Mixin definition: `(mixin Name body...)`
- [ ] Include mixins in classes: `(include MixinName)`
- [ ] Method copying from mixin to class

### 4. Properties
- [ ] Instance properties: `/property` notation
- [ ] Property access: `instance/property` or `(instance ./property)`
- [ ] Property assignment in constructors and methods

### 5. Advanced Features
- [ ] `on_extended` callback when class is extended
- [ ] `method_missing` for handling undefined methods
- [ ] Dynamic method invocation
- [ ] Bound methods (method + self reference)

## Implementation Steps

### Phase 1: Enhanced Class Support
1. Add namespace to Class type
2. Implement method storage in class
3. Add parent class chain traversal

### Phase 2: Method System
1. Add Method type with name and callable
2. Implement `.fn` method definition
3. Add method resolution and binding
4. Implement proper self binding

### Phase 3: Property System
1. Add instance property storage (Table[string, Value])
2. Implement property access instructions
3. Add property assignment in methods

### Phase 4: Mixins
1. Add Mixin type
2. Implement include mechanism
3. Method copying logic

### Phase 5: Advanced Features
1. Super with arguments
2. Callbacks (on_extended, method_missing)
3. Dynamic invocation

## VM Instructions Needed

### Existing (to enhance)
- `IkClass` - Create class with namespace
- `IkSubClass` - Create subclass with parent link
- `IkNew` - Instantiate with property table
- `IkResolveMethod` - Resolve through inheritance

### New Instructions
- `IkDefineMethod` - Define method in class
- `IkGetProperty` - Get instance property
- `IkSetProperty` - Set instance property
- `IkSuperCall` - Call parent method with args
- `IkMixin` - Create mixin
- `IkInclude` - Include mixin in class
- `IkCompileInit` - Compile class body
- `IkCallInit` - Execute class body

## Testing Strategy
1. Port tests from `gene-new/tests/test_oop.nim`
2. Start with basic class/instance tests
3. Add inheritance tests
4. Add method and property tests
5. Add mixin tests
6. Add advanced feature tests