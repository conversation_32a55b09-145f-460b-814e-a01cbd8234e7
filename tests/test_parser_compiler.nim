import unittest, times, strformat, streams
import ../src/gene/types
import ../src/gene/parser
import ../src/gene/compiler  
import ../src/gene/parser_compiler
import ../src/gene/vm

suite "Parser-Compiler Tests":
  
  test "Basic literals":
    let code = "42"
    let compiled = parse_compile_string(code)
    check compiled.instructions.len == 2  # push + return
    check compiled.instructions[0].kind == IkPushValue
    check compiled.instructions[0].arg0.kind == VkInt
    check compiled.instructions[0].arg0.int == 42
    
  test "String literal":
    let code = "\"hello world\""
    let compiled = parse_compile_string(code)
    check compiled.instructions.len == 2
    check compiled.instructions[0].kind == IkPushValue
    check compiled.instructions[0].arg0.kind == VkString
    check compiled.instructions[0].arg0.str == "hello world"
    
  test "Boolean literals":
    let code1 = "true"
    let compiled1 = parse_compile_string(code1)
    check compiled1.instructions[0].arg0.kind == VkBool
    check compiled1.instructions[0].arg0.bool == true
    
    let code2 = "false"
    let compiled2 = parse_compile_string(code2)
    check compiled2.instructions[0].arg0.kind == VkBool
    check compiled2.instructions[0].arg0.bool == false
    
  test "Nil literal":
    let code = "nil"
    let compiled = parse_compile_string(code)
    check compiled.instructions[0].arg0.kind == VkNil
    
  test "Array literal":
    let code = "[1 2 3]"
    let compiled = parse_compile_string(code)
    # Should have: ArrayStart, push 1, add, push 2, add, push 3, add, ArrayEnd, return
    check compiled.instructions[0].kind == IkArrayStart
    check compiled.instructions[^2].kind == IkArrayEnd
    check compiled.instructions[^1].kind == IkReturn
    
  test "Map literal":
    let code = "{:a 1 :b 2}"
    let compiled = parse_compile_string(code)
    # Should have: MapStart, push :a, push 1, set, push :b, push 2, set, MapEnd, return
    check compiled.instructions[0].kind == IkMapStart
    check compiled.instructions[^2].kind == IkMapEnd
    check compiled.instructions[^1].kind == IkReturn
    
  test "Simple function call":
    let code = "(+ 1 2)"
    let compiled = parse_compile_string(code)
    # Should have: GeneStart, resolve +, GeneSetType, push 1, GeneAddChild, push 2, GeneAddChild, GeneEnd, return
    check compiled.instructions[0].kind == IkGeneStart
    check compiled.instructions[^2].kind == IkGeneEnd
    check compiled.instructions[^1].kind == IkReturn
    
  test "Multiple expressions":
    let code = """
    1
    2
    3
    """
    let compiled = parse_compile_string(code)
    # Should have: push 1, pop, push 2, pop, push 3, return
    check compiled.instructions[0].kind == IkPushValue
    check compiled.instructions[1].kind == IkPop
    check compiled.instructions[2].kind == IkPushValue
    check compiled.instructions[3].kind == IkPop
    check compiled.instructions[4].kind == IkPushValue
    check compiled.instructions[5].kind == IkReturn

suite "Parser-Compiler Performance":
  
  proc benchmark_old_approach(code: string): float =
    let start = cpuTime()
    let parsed = read_all(code)
    let compiled = compile(parsed)
    result = cpuTime() - start
    
  proc benchmark_new_approach(code: string): float =
    let start = cpuTime()
    let compiled = parse_compile_string(code)
    result = cpuTime() - start
  
  test "Simple expression performance":
    let code = "(+ 1 2 3 4 5)"
    let old_time = benchmark_old_approach(code)
    let new_time = benchmark_new_approach(code)
    echo fmt"  Old approach: {old_time * 1000:.3f} ms"
    echo fmt"  New approach: {new_time * 1000:.3f} ms"
    echo fmt"  Speedup: {old_time / new_time:.2f}x"
    
  test "Complex nested expression performance":
    let code = """
    (let [x 10 y 20]
      (if (> x y)
        (* x 2)
        (+ y 3)))
    """
    let old_time = benchmark_old_approach(code)
    let new_time = benchmark_new_approach(code)
    echo fmt"  Old approach: {old_time * 1000:.3f} ms"
    echo fmt"  New approach: {new_time * 1000:.3f} ms"
    echo fmt"  Speedup: {old_time / new_time:.2f}x"
    
  test "Large file performance":
    # Generate a large code sample
    var code = ""
    for i in 0..<100:
      code.add(fmt"(def var{i} {i})\n")
      code.add(fmt"(+ var{i} 1)\n")
    
    let old_time = benchmark_old_approach(code)
    let new_time = benchmark_new_approach(code)
    echo fmt"  Old approach: {old_time * 1000:.3f} ms"
    echo fmt"  New approach: {new_time * 1000:.3f} ms"
    echo fmt"  Speedup: {old_time / new_time:.2f}x"
    
  test "Stream processing":
    # Test that streaming works correctly
    let code = "(+ 1 2)"
    let stream = newStringStream(code)
    let compiled = parse_compile_stream(stream)
    check compiled.instructions.len > 0
    check compiled.instructions[^1].kind == IkReturn

suite "Fail-Fast Error Handling":
  
  test "Unclosed string":
    expect ParseCompileError:
      discard parse_compile_string("\"hello")
      
  test "Unclosed array":
    expect ParseCompileError:
      discard parse_compile_string("[1 2 3")
      
  test "Unclosed gene":
    expect ParseCompileError:
      discard parse_compile_string("(+ 1 2")
      
  test "Invalid map syntax":
    expect ParseCompileError:
      discard parse_compile_string("{:a}")  # Missing value
      
  test "Early termination on error":
    # This should fail fast and not process the rest
    let code = """
    (def x 10)
    "unclosed string
    (def y 20)  ; This should never be reached
    """
    expect ParseCompileError:
      discard parse_compile_string(code)