import src/gene/parser_compiler_v2
import src/gene/types

proc test_code(code: string) =
  echo "\nTesting: ", code
  let compiled = parse_compile_string_v2(code)
  for i, instr in compiled.instructions:
    echo "  ", i, ": ", instr

# Test arithmetic operations
test_code("(+ 1 2)")
test_code("(- 5 3)")
test_code("(* 4 6)")
test_code("(/ 8 2)")

# Test more complex arithmetic
test_code("(+ 1 2 3 4)")

# Test literals
test_code("42")
test_code("\"hello\"")
test_code("true")

# Test arrays and maps
test_code("[1 2 3]")
test_code("{:a 1 :b 2}")