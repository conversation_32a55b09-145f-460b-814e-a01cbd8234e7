# Unified Parser-Compiler V2 for Gene
# This version uses lookahead to determine compilation strategy during parsing

import lexbase, streams, strutils, tables, sets, random
import ./types

type
  ParseCompileError* = object of CatchableError
  
  GeneCompileMode* = enum
    GcmLiteral      # Create a Gene object (_foo, quote, or in quote context)
    GcmArithmetic   # Direct arithmetic instruction (+, -, *, /, %)
    GcmSpecialForm  # Special compilation (if, let, fn, do, etc.)
    GcmFunction     # Function call (known functions)
    GcmRuntime      # Runtime resolution (unknown symbols)
    GcmMethod       # Method call (.method or obj.method)
    
  ParserCompilerV2* = object of BaseLexer
    filename*: string
    str*: string
    output*: CompilationUnit
    
    # Compilation state
    scope_tracker*: ScopeTracker
    quote_level*: int
    tail_position*: bool
    loop_stack*: seq[LoopInfo]
    
    # Parser state  
    error_occurred*: bool
    
    # Optimization flags
    fail_fast*: bool
    optimize*: bool
    
  ScopeTracker* = ref object
    parent*: ScopeTracker
    mappings*: Table[Key, int]
    var_index*: int
    
  LoopInfo* = object
    break_label*: int
    continue_label*: int

# Arithmetic operators that get direct instruction emission
const ARITHMETIC_OPS = {
  "+": IkAdd,
  "-": IkSub, 
  "*": IkMul,
  "/": IkDiv,
}.toTable()

# Special forms that need custom compilation
const SPECIAL_FORMS = [
  "if", "let", "do", "fn", "def", "var", "loop", "while", "for",
  "try", "throw", "return", "match", "quote", "class", "new",
  "break", "continue", "import", "ns"
].toHashSet()

# Known built-in functions
const BUILTIN_FUNCTIONS = [
  "println", "print", "str", "len", "first", "rest", "cons",
  "map", "filter", "reduce", "range", "not", "and", "or",
  "=", "!=", "<", ">", "<=", ">=", "type", "nil?"
].toHashSet()

# Forward declarations
proc parse_compile_v2*(self: var ParserCompilerV2): CompilationUnit
proc parse_compile_value(self: var ParserCompilerV2)
proc parse_compile_gene(self: var ParserCompilerV2)

# Helper functions
proc emit(self: var ParserCompilerV2, instr: Instruction) {.inline.} =
  self.output.instructions.add(instr)

proc emit_push_value(self: var ParserCompilerV2, value: Value) {.inline.} =
  self.emit(Instruction(kind: IkPushValue, arg0: value))

proc current_pos(self: ParserCompilerV2): int {.inline.} =
  self.output.instructions.len

proc error(self: var ParserCompilerV2, msg: string) =
  let line = self.lineNumber
  let col = self.getColNumber(self.bufpos)
  let error_msg = "$1($2:$3): $4" % [self.filename, $line, $col, msg]
  if self.fail_fast:
    raise newException(ParseCompileError, error_msg)
  else:
    echo "Error: ", error_msg

# Initialization
proc open*(self: var ParserCompilerV2, input: Stream, filename: string) =
  lexbase.open(self, input)
  self.filename = filename

proc new_parser_compiler_v2*(input: string, filename = "<input>"): ParserCompilerV2 =
  var stream = newStringStream(input)
  result = ParserCompilerV2(
    filename: filename,
    str: input,
    output: CompilationUnit(),
    scope_tracker: ScopeTracker(
      mappings: initTable[Key, int](),
      var_index: 0
    ),
    quote_level: 0,
    tail_position: false,
    loop_stack: @[],
    fail_fast: true,
    optimize: true
  )
  result.open(stream, filename)

# Whitespace and comment handling
proc skip_ws(self: var ParserCompilerV2) =
  while true:
    case self.buf[self.bufpos]
    of ' ', '\t', '\r':
      inc(self.bufpos)
    of '\l':
      inc(self.bufpos)
      inc(self.lineNumber)
    of '#':
      # Skip comment until end of line
      inc(self.bufpos)
      while self.buf[self.bufpos] notin {'\l', '\r', EndOfFile}:
        inc(self.bufpos)
    else:
      break

# Lookahead functions
proc peek_symbol(self: var ParserCompilerV2): string =
  ## Peek ahead to read the first symbol in a gene without consuming input
  let saved_pos = self.bufpos
  let saved_line = self.lineNumber
  
  # Skip the opening ( if we're positioned at it
  if self.buf[self.bufpos] == '(':
    inc(self.bufpos)
  
  self.skip_ws()
  var symbol = ""
  
  # Read symbol characters
  while self.buf[self.bufpos] notin {' ', '\t', '\r', '\l', '(', ')', '[', ']', '{', '}', '"', '#', '^', EndOfFile}:
    symbol.add(self.buf[self.bufpos])
    inc(self.bufpos)
  
  # Restore position
  self.bufpos = saved_pos
  self.lineNumber = saved_line
  
  return symbol

proc determine_gene_mode*(self: var ParserCompilerV2): GeneCompileMode =
  ## Look ahead to determine how this gene should be compiled
  
  # If we're in quote context, everything is literal
  if self.quote_level > 0:
    return GcmLiteral
  
  let first_symbol = self.peek_symbol()
  
  # Empty symbol means complex first element (number, string, nested gene, etc.)
  if first_symbol == "":
    return GcmRuntime
  
  # Check for literal gene marker
  if first_symbol == "_" or first_symbol == "quote":
    return GcmLiteral
  
  # Check for arithmetic operators
  if first_symbol in ARITHMETIC_OPS:
    return GcmArithmetic
  
  # Check for special forms
  if first_symbol in SPECIAL_FORMS:
    return GcmSpecialForm
  
  # Check for method calls
  if first_symbol.startsWith("."):
    return GcmMethod
  
  # Check for known built-in functions
  if first_symbol in BUILTIN_FUNCTIONS:
    return GcmFunction
  
  # Unknown - will be resolved at runtime
  return GcmRuntime

# Gene compilation based on mode
proc compile_arithmetic_gene(self: var ParserCompilerV2) =
  ## Compile arithmetic operations like (+, -, *, /, %)
  let op_symbol = self.peek_symbol()
  let instruction_kind = ARITHMETIC_OPS[op_symbol]
  
  # Skip the opening ( and operator
  inc(self.bufpos)  # Skip '('
  self.skip_ws()
  
  # Skip the operator symbol
  while self.buf[self.bufpos] notin {' ', '\t', '\r', '\l', ')', EndOfFile}:
    inc(self.bufpos)
  self.skip_ws()
  
  # For arithmetic, we need at least one argument
  var arg_count = 0
  
  # Compile first argument
  if self.buf[self.bufpos] != ')':
    self.parse_compile_value()
    arg_count = 1
    self.skip_ws()
  
  # Compile remaining arguments and emit operations
  while self.buf[self.bufpos] != ')':
    if self.buf[self.bufpos] == EndOfFile:
      self.error("Unexpected end of file in arithmetic expression")
      return
    
    self.parse_compile_value()
    self.emit(Instruction(kind: instruction_kind))
    arg_count += 1
    self.skip_ws()
  
  inc(self.bufpos)  # Skip ')'
  
  # Handle edge cases
  if arg_count == 0:
    # No arguments - push appropriate identity value
    case op_symbol:
    of "+": self.emit_push_value(0.to_value())
    of "*": self.emit_push_value(1.to_value())
    else: self.error(op_symbol & " requires at least one argument")

proc compile_function_gene(self: var ParserCompilerV2) =
  ## Compile known function calls
  inc(self.bufpos)  # Skip '('
  self.skip_ws()
  
  # Parse function name and arguments, then generate function call
  self.emit(Instruction(kind: IkGeneStartDefault, arg0: new_label().to_value()))
  
  # Compile function name
  self.parse_compile_value()
  self.emit(Instruction(kind: IkGeneSetType))
  
  # Compile arguments
  while self.buf[self.bufpos] != ')':
    if self.buf[self.bufpos] == EndOfFile:
      self.error("Unexpected end of file in function call")
      return
    
    self.parse_compile_value()
    self.emit(Instruction(kind: IkGeneAddChild))
    self.skip_ws()
  
  inc(self.bufpos)  # Skip ')'
  self.emit(Instruction(kind: IkGeneEnd, label: new_label()))

proc compile_literal_gene(self: var ParserCompilerV2) =
  ## Compile literal genes (create Gene objects)
  inc(self.bufpos)  # Skip '('
  self.skip_ws()
  
  if self.buf[self.bufpos] == ')':
    # Empty gene
    inc(self.bufpos)
    self.emit(Instruction(kind: IkGeneStart))
    self.emit(Instruction(kind: IkGeneEnd))
    return
  
  self.emit(Instruction(kind: IkGeneStart))
  
  # Compile type
  self.parse_compile_value()
  self.emit(Instruction(kind: IkGeneSetType))
  
  # Compile children
  while self.buf[self.bufpos] != ')':
    if self.buf[self.bufpos] == EndOfFile:
      self.error("Unexpected end of file in literal gene")
      return
    
    self.parse_compile_value()
    self.emit(Instruction(kind: IkGeneAddChild))
    self.skip_ws()
  
  inc(self.bufpos)  # Skip ')'
  self.emit(Instruction(kind: IkGeneEnd))

proc compile_special_form_gene(self: var ParserCompilerV2) =
  ## Compile special forms - for now, delegate to runtime
  ## TODO: Implement proper special form compilation
  self.compile_function_gene()  # Temporary fallback

proc compile_runtime_gene(self: var ParserCompilerV2) =
  ## Compile genes that need runtime resolution
  self.compile_function_gene()

proc compile_method_gene(self: var ParserCompilerV2) =
  ## Compile method calls
  self.compile_function_gene()  # For now, same as function calls

# Main gene parser with mode-based compilation
proc parse_compile_gene(self: var ParserCompilerV2) =
  let mode = self.determine_gene_mode()
  
  case mode:
  of GcmArithmetic:
    self.compile_arithmetic_gene()
  of GcmFunction:
    self.compile_function_gene()
  of GcmLiteral:
    self.compile_literal_gene()
  of GcmSpecialForm:
    self.compile_special_form_gene()
  of GcmRuntime:
    self.compile_runtime_gene()
  of GcmMethod:
    self.compile_method_gene()

# Number parsing
proc parse_compile_number(self: var ParserCompilerV2) =
  var pos = self.bufpos
  var is_float = false
  var num_str = ""
  
  # Handle negative numbers
  if self.buf[pos] == '-':
    num_str.add('-')
    inc(pos)
  
  # Parse digits
  while self.buf[pos] in {'0'..'9'}:
    num_str.add(self.buf[pos])
    inc(pos)
  
  # Check for decimal point
  if self.buf[pos] == '.':
    is_float = true
    num_str.add('.')
    inc(pos)
    while self.buf[pos] in {'0'..'9'}:
      num_str.add(self.buf[pos])
      inc(pos)
  
  # Check for exponent
  if self.buf[pos] in {'e', 'E'}:
    is_float = true
    num_str.add(self.buf[pos])
    inc(pos)
    if self.buf[pos] in {'+', '-'}:
      num_str.add(self.buf[pos])
      inc(pos)
    while self.buf[pos] in {'0'..'9'}:
      num_str.add(self.buf[pos])
      inc(pos)
  
  self.bufpos = pos
  
  # Emit the appropriate instruction
  if is_float:
    let value = parseFloat(num_str)
    self.emit_push_value(value.to_value())
  else:
    let value = parseInt(num_str)
    self.emit_push_value(value.to_value())

# String parsing
proc parse_compile_string(self: var ParserCompilerV2) =
  var str = ""
  inc(self.bufpos)  # Skip opening quote
  
  while true:
    let ch = self.buf[self.bufpos]
    if ch == EndOfFile:
      self.error("Unexpected end of file in string")
      return
    elif ch == '"':
      inc(self.bufpos)  # Skip closing quote
      break
    elif ch == '\\':
      inc(self.bufpos)
      let escape = self.buf[self.bufpos]
      case escape
      of 'n': str.add('\n')
      of 't': str.add('\t')
      of 'r': str.add('\r')
      of '\\': str.add('\\')
      of '"': str.add('"')
      else:
        str.add(escape)
      inc(self.bufpos)
    else:
      str.add(ch)
      inc(self.bufpos)
  
  self.emit_push_value(str.to_value())

# Symbol parsing
proc parse_compile_symbol(self: var ParserCompilerV2) =
  var sym = ""
  
  # Parse symbol characters
  while self.buf[self.bufpos] notin {' ', '\t', '\r', '\l', '(', ')', '[', ']', '{', '}', '"', '#', EndOfFile}:
    sym.add(self.buf[self.bufpos])
    inc(self.bufpos)
  
  if sym.len == 0:
    self.error("Empty symbol")
    return
  
  # Handle special symbols
  case sym
  of "nil":
    self.emit_push_value(NIL)
  of "true":
    self.emit_push_value(true.to_value())
  of "false":
    self.emit_push_value(false.to_value())
  else:
    # Check if it's a keyword (starts with :)
    if sym[0] == ':':
      self.emit_push_value(sym[1..^1].to_key().to_value())
    else:
      # Regular symbol - check if it's a local variable
      let key = sym.to_key()
      if self.scope_tracker.mappings.hasKey(key):
        let index = self.scope_tracker.mappings[key]
        self.emit(Instruction(kind: IkVarResolve, arg0: index.to_value()))
      else:
        # Global symbol resolution
        self.emit(Instruction(kind: IkResolveSymbol, arg0: key.to_value()))

# Array parsing (simplified - space separated)
proc parse_compile_array(self: var ParserCompilerV2) =
  inc(self.bufpos)  # Skip '['
  
  self.emit(Instruction(kind: IkArrayStart))
  
  self.skip_ws()
  while self.buf[self.bufpos] != ']':
    if self.buf[self.bufpos] == EndOfFile:
      self.error("Unexpected end of file in array")
      return
    
    self.parse_compile_value()
    self.emit(Instruction(kind: IkArrayAddChild))
    self.skip_ws()
  
  inc(self.bufpos)  # Skip ']'
  self.emit(Instruction(kind: IkArrayEnd))

# Map parsing (key value pairs)
proc parse_compile_map(self: var ParserCompilerV2) =
  inc(self.bufpos)  # Skip '{'
  
  self.emit(Instruction(kind: IkMapStart))
  
  self.skip_ws()
  while self.buf[self.bufpos] != '}':
    if self.buf[self.bufpos] == EndOfFile:
      self.error("Unexpected end of file in map")
      return
    
    # Parse key and value
    self.parse_compile_value()
    self.skip_ws()
    self.parse_compile_value()
    self.emit(Instruction(kind: IkMapSetProp))
    self.skip_ws()
  
  inc(self.bufpos)  # Skip '}'
  self.emit(Instruction(kind: IkMapEnd))

# Main value parser
proc parse_compile_value(self: var ParserCompilerV2) =
  self.skip_ws()
  
  let ch = self.buf[self.bufpos]
  case ch
  of EndOfFile:
    self.error("Unexpected end of file")
  of '(':
    self.parse_compile_gene()
  of '[':
    self.parse_compile_array()
  of '{':
    self.parse_compile_map()
  of '"':
    self.parse_compile_string()
  of '-', '0'..'9':
    self.parse_compile_number()
  else:
    self.parse_compile_symbol()

# Main entry point
proc parse_compile_v2*(self: var ParserCompilerV2): CompilationUnit =
  # Parse and compile all top-level expressions
  var expr_count = 0
  
  while true:
    self.skip_ws()
    if self.buf[self.bufpos] == EndOfFile:
      break
    
    # Set tail position for the last expression
    if expr_count > 0:
      self.emit(Instruction(kind: IkPop))  # Pop previous expression result
    
    self.parse_compile_value()
    inc(expr_count)
  
  # Add final return instruction
  if expr_count == 0:
    self.emit_push_value(NIL)
  
  self.emit(Instruction(kind: IkReturn))
  
  return self.output

# Public API
proc parse_compile_string_v2*(input: string, filename = "<input>"): CompilationUnit =
  var pc = new_parser_compiler_v2(input, filename)
  return pc.parse_compile_v2()